import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/security/session_manager.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/app_drawer/view/app_drawer.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/folders/view/folder_department_page.dart';
import 'package:paperless_mobile/features/folders/view/folder_project_page.dart';
import 'package:paperless_mobile/features/labels/view/pages/labels_page.dart';
import 'package:paperless_mobile/features/landing/view/widgets/menu_search_user.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class FolderPage extends StatelessWidget {
  const FolderPage({super.key});

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Scaffold(
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
            child: MenuSearchUser(),
          ),
          const Gap(12),
          Text(
            'FOLDERS',
            style: AppTextStyles.textStyleBold14
                .copyWith(color: AppColor.grey_909090),
          ),
          const Gap(8),
          _buildItemFolder(
              size: size,
              title: 'Labels',
              svgPath: 'assets/svgs/labels_blue.svg',
              isFirstItem: true,
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const LabelsPage()));
              }),
          _buildItemFolder(
              size: size,
              title: 'Departments',
              svgPath: 'assets/svgs/department.svg',
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => BlocProvider(
                              create: (context) => DocumentUploadCubit(
                                  context.read(),
                                  context.read(),
                                  context.read(),
                                  context.read(),
                                  PaperlessTasksApiImpl(
                                      context.read<SessionManager>().client))
                                ..getAllDepartment(),
                              child: const FolderDepartmentPage(),
                            )));
              }),
          _buildItemFolder(
              size: size,
              title: 'Projects',
              isLastItem: true,
              svgPath: 'assets/svgs/project.svg',
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => BlocProvider(
                              create: (context) => DocumentUploadCubit(
                                  context.read(),
                                  context.read(),
                                  context.read(),
                                  context.read(),
                                  PaperlessTasksApiImpl(
                                      context.read<SessionManager>().client))
                                ..getAllProject(),
                              child: const FolderProjectPage(),
                            )));
              }),
        ]),
      ),
    );
  }

  Widget _buildItemFolder({
    required Size size,
    required String title,
    required String svgPath,
    required VoidCallback onTap,
    bool isLastItem = false,
    bool isFirstItem = false,
  }) {
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: Container(
        height: 46,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
            color: AppColor.white,
            borderRadius: BorderRadius.only(
                topLeft: isFirstItem ? const Radius.circular(10) : Radius.zero,
                topRight: isFirstItem ? const Radius.circular(10) : Radius.zero,
                bottomLeft:
                    isLastItem ? const Radius.circular(10) : Radius.zero,
                bottomRight:
                    isLastItem ? const Radius.circular(10) : Radius.zero)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(),
            Row(children: [
              const Gap(14),
              SvgPicture.asset(svgPath),
              const Gap(12),
              Text(
                title,
                style: AppTextStyles.textStyle14.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              const Icon(Icons.chevron_right),
              const Gap(14),
            ]),
            Row(
              children: [
                const Gap(50),
                if (!isLastItem)
                  Container(
                    height: 1,
                    width: size.width - 82,
                    color: AppColor.black_3C3C43.withOpacity(0.3),
                  )
              ],
            )
          ],
        ),
      ),
    );
  }
}
